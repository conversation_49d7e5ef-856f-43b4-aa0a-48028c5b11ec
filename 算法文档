1. 概述
本文档详细描述对敲交易风控算法的设计与实现流程。通过先进的仓位画像分析和多维度特征匹配，精准识别同账户及跨账户的对敲（Wash Trading）行为。算法的核心是构建完整的仓位生命周期视图，并基于此进行高效的匹配和评分，最终输出结构化的风险数据。

2. 核心目标
- 精准识别: 有效识别同一账户内或跨不同账户间的反向、对敲交易行为
- 量化风险: 对检测到的可疑交易对进行多维度特征分析，并输出量化的风险评分（wash_score）和风险等级
- 流程自动化: 实现从原始交易数据输入到最终风险结果输出的全流程自动化处理
- 高可配置性: 算法的关键参数（如时间窗口、金额容差等）可通过配置文件进行灵活调整，以适应不同市场环境和风控策略
- 性能优化: 采用高效的预筛选和索引机制，确保在大数据量下的检测性能

3. 算法实现流程
算法整体流程分为三个主要阶段：数据预处理、核心检测逻辑、风险评分与输出。

3.1 数据预处理与仓位构建
此阶段是所有检测的基础，目标是将离散的交易流水（Fills）聚合成结构化的"完整仓位"（Complete Position），并确保数据的完整性。

3.1.1 时间维度查询
- 算法的入口点支持传入时间范围参数 (start_time, end_time)
- 从海量的交易流水数据中，仅提取此时间窗口内发生的交易记录，避免全量数据扫描，提升效率

3.1.2 数据标准化
- 输入指定时间范围内的原始交易数据（DataFrame）
- 根据 wash_trading_config.yml 中的 required_fields 映射和验证字段，确保数据完整性和一致性
- 必要字段包括：member_id, contract_name, side, deal_vol_usdt, timestamp, position_id 等

3.1.3 完整仓位构建与完整性验证
此步骤是确保数据质量的核心。系统首先根据时间窗口内的交易数据尝试构建仓位，然后验证其完整性，以排除因时间切割造成的数据不完整问题。

分组处理
- 系统将筛选后的交易记录按 position_id 进行分组
初步构建
对于每个 position_id 分组，系统会聚合其所有的开仓和平仓记录，并计算构建一个 CompletePosition 对象。该对象包含仓位的完整信息：
- member_id: 用户ID
- contract_name: 合约名称
- position_id: 仓位ID
- primary_side: 主要方向（多/空）
- first_open_time, last_open_time: 首次/末次开仓时间
- first_close_time, last_close_time: 首次/末次平仓时间
- total_open_amount, total_close_amount: 总开/平仓金额
- avg_open_price, avg_close_price: 平均开/平仓价格
- real_profit: 已实现盈亏
- total_duration_minutes: 持仓总时长
- is_completed: 仓位是否已完全平仓
完整性验证（排除不完整数据）
在初步构建 CompletePosition 对象后，将进行严格的验证。任何不满足以下条件的仓位都将被视为不完整数据并被排除，不会进入后续的对敲检测环节：
- 开仓时间必须在窗口内: 仓位的 first_open_time 不得早于查询的 start_time。这确保了我们拥有该仓位完整的开仓历史，从而保证开仓金额、均价等指标的准确性
- 必须在窗口内完全平仓: 仓位在查询的 end_time 时，其状态必须是已完成 (is_completed = True)。这确保了其最终盈亏和持仓周期是确定且完整的
最终产出
只有那些开仓和平仓行为均完整地发生在 [start_time, end_time] 时间窗口内的仓位，才会被送入后续的检测流程。

3.2 核心检测逻辑
在构建了完整且经过验证的仓位画像后，系统进入核心的对敲检测阶段。检测分为同账户和跨账户两种模式。

3.2.1 同账户对敲检测 (Same-Account Wash Trading)
此模式用于检测单个账户内自己与自己进行对手交易的行为。
检测步骤
1. 分组: 按 (member_id, contract_name) 对所有开仓记录进行分组
2. 匹配: 在每个组内，将开多（side=1）和开空（side=3）的记录分开
3. 遍历匹配:
  - 遍历所有开多记录
  - 对于每一笔开多记录，在 open_time_window (e.g., 15秒) 时间窗口内，查找是否存在一笔或多笔开空记录
  - 金额匹配: 检查找到的开多/开空仓位金额是否在 amount_matching 配置的容差范围内
  - 仓位关联: 找到匹配的交易后，系统会进一步关联其完整的 CompletePosition 信息进行深度分析
4. 生成风险: 如果时间、金额、方向均满足预设条件，则判定为一次可疑的同账户对敲，并记录下来

3.2.2 跨账户对敲检测 (Cross-Account Wash Trading)
此模式检测不同账户之间协同进行的反向交易。
检测步骤
5. 预筛选:
  - 按 contract_name 对所有已完成的完整仓位 (CompletePosition) 进行分组
  - 在每个合约分组内，进行两两仓位 (pos_a, pos_b) 的比较
6. 匹配条件:
  - 交易对手: pos_a.member_id != pos_b.member_id
  - 方向相反: pos_a.primary_side != pos_b.primary_side
  - 时间窗口: 两个仓位的首次开仓时间差 (first_open_time) 在 wash_trading_time_window (e.g., 30秒) 内
  - 金额匹配: 采用阶梯式绝对容差与相对容差相结合的策略。系统将根据交易金额的大小（取两个仓位中的较大值），应用不同的容差标准
7. 深度分析与评分:
  - 对于满足上述所有预筛选条件的仓位对，调用 _analyze_wash_trading_pair 方法进行深度分析和评分
  
3.3 风险评分与等级划分
风险评分是对可疑交易对的风险程度进行量化评估的关键步骤。采用多维度特征分析和智能权重分配，确保评分的准确性和可靠性。

3.3.1 评分维度详解
盈亏对敲分数 (profit_hedge_score) - 核心特征
- 重要性: 对敲交易最核心的特征，双方盈亏基本抵消是对敲行为的本质特征
- 计算逻辑: 
    # 基础检查
    total_profit = profit_a + profit_b
    profit_sum = abs(profit_a) + abs(profit_b)

    # 特殊情况：总盈亏为0（完全抵消，包括无盈亏情况）
    if abs(total_profit) == 0:
        return 0.8  # 给予较高分数，这也是对敲特征

    # 判断是否同边
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)

    if not is_same_side:
        # 异边（一盈一亏）：和原有的保持一致
        return 1.0 - abs(total_profit) / profit_sum
    
    else:
        # 同边（双盈/双亏）：优化后的接近0 + 相对波动率
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)

        
        # 交易量规模
        scale = order_amount_a + order_amount_b
        
        # 优化的接近0程度评分（放宽阈值）
        min_relative = min_abs / scale
        if min_relative <= 0.001:      # ≤0.1%，几乎无盈亏
            closeness_score = 0.95
        elif min_relative <= 0.003:    # ≤0.3%，很接近0
            closeness_score = 0.9
        elif min_relative <= 0.005:    # ≤0.5%，接近0
            closeness_score = 0.8
        elif min_relative <= 0.01:     # ≤1%，比较接近0
            closeness_score = 0.6
        elif min_relative <= 0.02:     # ≤2%，稍微接近0
            closeness_score = 0.4
        elif min_relative <= 0.05:     # ≤5%，不太接近0
            closeness_score = 0.2
        else:                          # >5%，远离0
            closeness_score = 0.1
        
        # 相对波动率评分（保持不变）
        rate_a = abs_a / order_amount_a if order_amount_a > 0 else 0
        rate_b = abs_b / order_amount_b if order_amount_b > 0 else 0
        
        min_rate = min(rate_a, rate_b)
        max_rate = max(rate_a, rate_b)
        # 比值越小，差异越大，越可能是对敲
        if max_rate > 0:
            rate_ratio = min_rate / max_rate
            
            if rate_ratio <= 0.1:        # 差异巨大（小方是大方的10%以下）
                volatility_score = 0.9
            elif rate_ratio <= 0.2:      # 差异很大（小方是大方的20%以下）
                volatility_score = 0.8
            elif rate_ratio <= 0.5:      # 差异较大（小方是大方的50%以下）
                volatility_score = 0.6
            elif rate_ratio <= 0.8:      # 差异一般（小方是大方的80%以下）
                volatility_score = 0.4
            else:                        # 差异很小（比值>0.8）
                volatility_score = 0.2
        else:
            volatility_score = 0.5
        
        # 权重：接近0(60%) + 相对波动率(40%)
        return 0.6 * closeness_score + 0.4 * volatility_score
    
        return score

- 分数解释: 分数越高表示盈亏对敲特征越明显，1.0表示完全对敲，0.0表示无对敲特征

时间匹配分数 (time_match_score) - 同步性特征
- 重要性: 时间同步性是对敲交易的重要识别特征
- 计算逻辑: 采用指数衰减函数，让时间越接近分数越高
time_diff = abs(开仓时间差)
normalized_diff = time_diff / time_window

if normalized_diff >= 1.0:
    time_match_score = 0.0
else:
    time_match_score = exp(-2 * normalized_diff)  # 指数衰减
- 优势: 前5秒内的匹配获得更高分数，更符合实际对敲行为特征

金额匹配分数 (amount_match_score) - 规模一致性
- 重要性: 金额相似性反映对敲交易的规模协调性
- 计算逻辑: 真正的阶梯式容差计算
def calculate_amount_tolerance(amount_a: float, amount_b: float) -> float:
    """计算金额容差"""
    max_amount = max(amount_a, amount_b)
    
    if max_amount <= 100:
        # 极小额：10%相对容差，最小2 USDT
        return max(max_amount * 0.10, 2.0)
    elif max_amount <= 1000:
        # 小额：5%相对容差，最小5 USDT
        return max(max_amount * 0.05, 5.0)
    elif max_amount <= 10000:
        # 中额：3%相对容差或50 USDT，取较小值
        return min(max_amount * 0.03, 50.0)
    elif max_amount <= 100000:
        # 大额：2%相对容差或200 USDT，取较小值
        return min(max_amount * 0.02, 200.0)
    else:
        # 超大额：1.5%相对容差
        return max_amount * 0.015

分数计算：
def calculate_optimized_amount_match_score(amount_a: float, amount_b: float) -> float:
    amount_diff = abs(amount_a - amount_b)
    max_amount = max(amount_a, amount_b)
    
    if max_amount == 0:
        return 0.0
    
    tolerance = calculate_amount_tolerance(amount_a, amount_b)
    
    if amount_diff == 0:
        return 1.0
    elif amount_diff <= tolerance:
        # 容差内：0.80-1.0 的线性评分
        score = 1.0 - 0.2 * (amount_diff / tolerance)
    else:
        # 容差外：0-0.8 的指数衰减评分
        excess_ratio = (amount_diff - tolerance) / tolerance
        score = 0.80 * math.exp(-excess_ratio * 0.75) 
    
    return max(0.0, min(1.0, score))

持仓时长相似度 (duration_similarity) - 行为一致性
- 重要性: 持仓时长的相似性反映交易策略的协调性
- 计算公式: 1.0 - min(时长差 / 最大时长, 1.0)
- 作用: 辅助特征，用于识别协调性对敲行为

3.3.2 综合评分 (wash_score) - 优化权重分配
权重配置 (基于特征重要性优化):
- 盈亏对敲 (0.4): 最核心特征，权重最高
- 时间匹配 (0.25): 重要的同步性特征  
- 金额匹配 (0.25): 重要的规模特征
- 持仓时长 (0.1): 辅助特征，权重较低
计算公式: 
wash_score = profit_hedge_score * 0.4 + 
             time_match_score * 0.25 + 
             amount_match_score * 0.25 + 
             duration_similarity * 0.1

3.3.3 置信度评估 (confidence_score) - 新增特征
目的: 评估检测结果的可靠性，提供决策支持
计算维度:
- 交易量置信度: min(1.0, min_volume / 10000)
- 数据完整性: 1.0 (完整) 或 0.7 (不完整)
- 时间窗口置信度: 1.0 - (time_diff / time_window)
综合置信度: 各维度平均值

3.3.4 动态风险等级划分
- Critical (极高风险): profit_hedge_score > 0.9 且 time_match_score > 0.8
- High (高风险): wash_score > 0.85
- Medium (中风险): wash_score > 0.7  
- Low (低风险): wash_score > 0.5
- Minimal (极低风险): wash_score ≤ 0.5

3.3.5 风险判定逻辑
- 主要条件: wash_score > wash_score_threshold (0.7)
- 核心条件: profit_hedge_score > profit_hedge_threshold (0.7)
- 最终判定: 同时满足主要条件和核心条件时，判定为对敲交易
- 置信度要求: confidence_score > 0.6 时结果更可信

4. 数据流
下表描述了数据从原始记录到最终风险结果的完整流转过程。
阶段
数据源/输入
处理模块/动作
输出数据/格式
关键字段
1. 初始数据
交易流水数据库/文件, 查询时间范围 (start_time, end_time)
数据提取和加载 (按时间过滤)
pandas.DataFrame
member_id, digital_id, contract_name, side, price, volume, deal_vol_usdt, timestamp, position_id
2. 仓位构建
交易流水 DataFrame
PositionBasedOptimizer
CompletePosition 对象集合
member_id, contract_name, first_open_time, total_open_amount, real_profit, is_completed
3. 数据清洗
CompletePosition 对象集合
不完整数据排除模块
清洗后的 CompletePosition 集合
first_open_time, is_completed
4. 对敲检测
清洗后的 CompletePosition 集合
optimized_wash_trading_detection
疑似对敲交易对（字典列表）
pos_a, pos_b, is_same_account, wash_score, is_wash_trading
5. 评分计算
疑似对敲交易对
_analyze_wash_trading_pair
详细评分结果
profit_hedge_score, time_match_score, amount_match_score, duration_similarity, confidence_score
6. 风险分级
详细评分结果
动态风险等级划分
风险等级标记
severity (Critical/High/Medium/Low/Minimal)
7. 结果存储
最终风险结果
_save_wash_trading_details
wash_trading_pairs 数据库表
user_a_id, user_b_id, contract_name, severity, risk_score, confidence_score, user_a_open_time, user_a_open_amount, user_a_close_time, user_a_close_amount, user_a_profit, open_time_diff_seconds, net_profit

5. 关键配置项 (wash_trading_config.yml)
以下是控制算法行为的核心参数，可通过修改配置文件进行调整。
分类
参数名
描述
推荐值
调优说明
时间阈值
open_time_window
同账户对敲的开仓时间匹配窗口（秒）
30
可根据市场活跃度调整，活跃市场可缩短至10秒

time_window
跨账户对敲的开仓时间匹配窗口（秒）
30
跨账户协调需要更长时间，建议保持30秒
评分权重
profit_hedge_weight
盈亏对敲特征权重
0.4
核心特征，建议保持较高权重

time_match_weight
时间匹配特征权重
0.25
重要特征，可根据时间精度要求调整

amount_match_weight
金额匹配特征权重
0.25
重要特征，可根据金额精度要求调整

duration_weight
持仓时长特征权重
0.1
辅助特征，权重较低
风险阈值
wash_score_threshold
判定为对敲的综合风险分阈值
0.7
可调整：0.6(宽松) - 0.8(严格)

profit_hedge_threshold
盈亏对敲分数阈值
0.7
核心阈值，建议保持0.7以上

confidence_threshold
置信度阈值
0.6
新增：结果可信度要求
功能开关
same_account.enabled
是否启用同账户对敲检测
true
建议始终启用

cross_account.enabled
是否启用跨账户对敲检测
true
建议始终启用

confidence_check.enabled
是否启用置信度检查
true
新增：提高结果可靠性

5.1 优化后的金额匹配配置 (amount_matching)
amount_matching:
  # 基于金额比例的容差设计
  tolerance_tiers:
    - amount_range: [0, 100]         # 极小额交易
      tolerance_type: "relative"
      tolerance: 0.10                # 10% 相对容差
      min_absolute: 2                # 最小2 USDT绝对容差
      description: "极小额交易，严格相对容差"
      
    - amount_range: [100, 1000]      # 小额交易  
      tolerance_type: "relative"
      tolerance: 0.05                # 5% 相对容差
      min_absolute: 5                # 最小5 USDT绝对容差
      description: "小额交易，适中相对容差"
      
    - amount_range: [1000, 10000]    # 中额交易
      tolerance_type: "hybrid"       # 混合模式
      relative_tolerance: 0.03       # 3% 相对容差
      absolute_tolerance: 50         # 或50 USDT绝对容差，取较小值
      description: "中额交易，混合容差策略"
      
    - amount_range: [10000, 100000]  # 大额交易
      tolerance_type: "hybrid"
      relative_tolerance: 0.02       # 2% 相对容差  
      absolute_tolerance: 200        # 或200 USDT绝对容差，取较小值
      description: "大额交易，混合容差策略"
      
    - amount_range: [100000, 999999999]  # 超大额交易
      tolerance_type: "relative"
      tolerance: 0.015               # 1.5% 相对容差
      description: "超大额交易，纯相对容差"

  # 特殊场景配置
  special_scenarios:
    # 活动奖励套利检测
    reward_arbitrage:
      enabled: true
      amount_range: [5, 100]         # 5-100 USDT范围
      tolerance: 0.05                # 5% 严格容差
      description: "针对活动奖励套利的严格检测"
  

### 5.2 新增评分配置 (scoring_config)

```yaml
scoring_config:
# 权重配置
  weights:
    profit_hedge: 0.4      # 盈亏对敲权重 (最重要)
    time_match: 0.25       # 时间匹配权重
    amount_match: 0.25     # 金额匹配权重
    duration: 0.1          # 持仓时长权重 (最低)

# 时间匹配评分参数
  time_scoring:
    decay_function: "exponential"  # 指数衰减函数
    decay_rate: 2.0               # 衰减率
    high_score_window: 5          # 高分时间窗口(秒)

# 置信度评分参数
  confidence_scoring:
    volume_baseline: 10000        # 交易量基准 (USDT)
    completeness_bonus: 0.3       # 数据完整性加分
    time_window_factor: 1.0       # 时间窗口因子
 
### 5.3 风险等级配置 (risk_levels)

```yaml
risk_levels:
  critical:
    wash_score_min: 0.9
    profit_hedge_min: 0.9
    time_match_min: 0.8
    description: "极高风险，强烈建议人工审核"

  high:
    wash_score_min: 0.85
    description: "高风险，需要重点关注"

  medium:
    wash_score_min: 0.7
    description: "中风险，建议监控"


