#!/usr/bin/env python3
"""
交易数据记录系统
记录对敲检测测试的详细交易数据
"""

import json
import csv
import sqlite3
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import os


@dataclass
class TradeRecord:
    """交易记录"""
    # 基本信息
    record_id: str
    test_case_id: int
    coin: str
    symbol: str
    
    # 交易参数
    usdt_amount: float
    orderqty: int
    planned_wait_seconds: int
    actual_wait_seconds: float
    
    # 买入信息
    buy_order_id: str
    buy_time: str
    buy_success: bool
    buy_error_message: Optional[str]
    
    # 卖出信息
    sell_order_id: str
    sell_time: str
    sell_success: bool
    sell_error_message: Optional[str]
    
    # 时间统计
    start_time: str
    end_time: str
    total_duration_seconds: float
    
    # 交易结果
    trade_success: bool
    profit_loss: Optional[float]  # 盈亏（如果能获取到）
    
    # 对敲检测相关
    test_type: str  # 'normal', 'wash_trading_suspect'
    time_match_score: Optional[float]  # 时间匹配分数
    amount_match_score: Optional[float]  # 金额匹配分数
    
    # 元数据
    created_at: str
    notes: Optional[str]


class TradeRecorder:
    """交易数据记录器"""
    
    def __init__(self, db_path: str = "wash_trading_test.db"):
        """初始化记录器
        
        Args:
            db_path: SQLite数据库文件路径
        """
        self.db_path = db_path
        self.records = []
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建交易记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trade_records (
                record_id TEXT PRIMARY KEY,
                test_case_id INTEGER,
                coin TEXT,
                symbol TEXT,
                usdt_amount REAL,
                orderqty INTEGER,
                planned_wait_seconds INTEGER,
                actual_wait_seconds REAL,
                buy_order_id TEXT,
                buy_time TEXT,
                buy_success BOOLEAN,
                buy_error_message TEXT,
                sell_order_id TEXT,
                sell_time TEXT,
                sell_success BOOLEAN,
                sell_error_message TEXT,
                start_time TEXT,
                end_time TEXT,
                total_duration_seconds REAL,
                trade_success BOOLEAN,
                profit_loss REAL,
                test_type TEXT,
                time_match_score REAL,
                amount_match_score REAL,
                created_at TEXT,
                notes TEXT
            )
        ''')
        
        # 创建统计表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_statistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_session_id TEXT,
                total_tests INTEGER,
                successful_tests INTEGER,
                failed_tests INTEGER,
                total_duration_seconds REAL,
                avg_wait_time REAL,
                coin_distribution TEXT,  -- JSON格式
                test_type_distribution TEXT,  -- JSON格式
                created_at TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def record_trade(self, trade_result: Dict[str, Any], test_case_id: int, 
                    test_type: str = 'normal') -> TradeRecord:
        """记录单次交易
        
        Args:
            trade_result: 统一交易器返回的交易结果
            test_case_id: 测试用例ID
            test_type: 测试类型
            
        Returns:
            交易记录对象
        """
        record_id = f"{test_case_id}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        # 提取交易信息
        if trade_result.get('success'):
            buy_result = trade_result.get('buy_result', {})
            sell_result = trade_result.get('sell_result', {})
            
            record = TradeRecord(
                record_id=record_id,
                test_case_id=test_case_id,
                coin=trade_result.get('coin', ''),
                symbol=buy_result.get('coin', '').lower() + '-usdt',
                usdt_amount=trade_result.get('usdt_amount', 0),
                orderqty=trade_result.get('orderqty', 0),
                planned_wait_seconds=trade_result.get('planned_wait_seconds', 0),
                actual_wait_seconds=trade_result.get('actual_wait_seconds', 0),
                buy_order_id=trade_result.get('buy_order_id', ''),
                buy_time=trade_result.get('buy_time', ''),
                buy_success=buy_result.get('success', False),
                buy_error_message=buy_result.get('message'),
                sell_order_id=trade_result.get('sell_order_id', ''),
                sell_time=trade_result.get('sell_time', ''),
                sell_success=sell_result.get('success', False),
                sell_error_message=sell_result.get('message'),
                start_time=trade_result.get('start_time', ''),
                end_time=trade_result.get('end_time', ''),
                total_duration_seconds=trade_result.get('total_duration_seconds', 0),
                trade_success=True,
                profit_loss=None,  # 暂时无法获取实际盈亏
                test_type=test_type,
                time_match_score=None,  # 后续计算
                amount_match_score=None,  # 后续计算
                created_at=datetime.now().isoformat(),
                notes=None
            )
        else:
            # 失败的交易
            record = TradeRecord(
                record_id=record_id,
                test_case_id=test_case_id,
                coin=trade_result.get('coin', ''),
                symbol=trade_result.get('coin', '').lower() + '-usdt' if trade_result.get('coin') else '',
                usdt_amount=trade_result.get('usdt_amount', 0),
                orderqty=0,
                planned_wait_seconds=0,
                actual_wait_seconds=0,
                buy_order_id='',
                buy_time='',
                buy_success=False,
                buy_error_message=trade_result.get('buy_result', {}).get('message') if 'buy_result' in trade_result else trade_result.get('message'),
                sell_order_id='',
                sell_time='',
                sell_success=False,
                sell_error_message=trade_result.get('sell_result', {}).get('message') if 'sell_result' in trade_result else None,
                start_time=datetime.now().isoformat(),
                end_time=datetime.now().isoformat(),
                total_duration_seconds=0,
                trade_success=False,
                profit_loss=None,
                test_type=test_type,
                time_match_score=None,
                amount_match_score=None,
                created_at=datetime.now().isoformat(),
                notes=f"交易失败: {trade_result.get('error', 'unknown')}"
            )
        
        # 保存到内存和数据库
        self.records.append(record)
        self._save_to_database(record)
        
        return record
    
    def _save_to_database(self, record: TradeRecord):
        """保存记录到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO trade_records VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
            )
        ''', (
            record.record_id, record.test_case_id, record.coin, record.symbol,
            record.usdt_amount, record.orderqty, record.planned_wait_seconds,
            record.actual_wait_seconds, record.buy_order_id, record.buy_time,
            record.buy_success, record.buy_error_message, record.sell_order_id,
            record.sell_time, record.sell_success, record.sell_error_message,
            record.start_time, record.end_time, record.total_duration_seconds,
            record.trade_success, record.profit_loss, record.test_type,
            record.time_match_score, record.amount_match_score,
            record.created_at, record.notes
        ))
        
        conn.commit()
        conn.close()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.records:
            return {}
        
        total_tests = len(self.records)
        successful_tests = len([r for r in self.records if r.trade_success])
        failed_tests = total_tests - successful_tests
        
        # 时间统计
        durations = [r.total_duration_seconds for r in self.records if r.trade_success]
        total_duration = sum(durations) if durations else 0
        avg_duration = total_duration / len(durations) if durations else 0
        
        wait_times = [r.actual_wait_seconds for r in self.records if r.trade_success]
        avg_wait_time = sum(wait_times) / len(wait_times) if wait_times else 0
        
        # 币种分布
        coin_distribution = {}
        for record in self.records:
            coin = record.coin
            if coin not in coin_distribution:
                coin_distribution[coin] = {'total': 0, 'success': 0, 'failed': 0}
            coin_distribution[coin]['total'] += 1
            if record.trade_success:
                coin_distribution[coin]['success'] += 1
            else:
                coin_distribution[coin]['failed'] += 1
        
        # 测试类型分布
        test_type_distribution = {}
        for record in self.records:
            test_type = record.test_type
            if test_type not in test_type_distribution:
                test_type_distribution[test_type] = {'total': 0, 'success': 0, 'failed': 0}
            test_type_distribution[test_type]['total'] += 1
            if record.trade_success:
                test_type_distribution[test_type]['success'] += 1
            else:
                test_type_distribution[test_type]['failed'] += 1
        
        return {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'failed_tests': failed_tests,
            'success_rate': successful_tests / total_tests * 100 if total_tests > 0 else 0,
            'total_duration_seconds': total_duration,
            'avg_duration_seconds': avg_duration,
            'avg_wait_time_seconds': avg_wait_time,
            'coin_distribution': coin_distribution,
            'test_type_distribution': test_type_distribution
        }
    
    def export_to_csv(self, filename: str):
        """导出到CSV文件"""
        if not self.records:
            print("❌ 没有记录可导出")
            return
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'record_id', 'test_case_id', 'coin', 'symbol', 'usdt_amount', 'orderqty',
                'planned_wait_seconds', 'actual_wait_seconds', 'buy_order_id', 'buy_time',
                'buy_success', 'sell_order_id', 'sell_time', 'sell_success',
                'total_duration_seconds', 'trade_success', 'test_type', 'created_at'
            ]
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for record in self.records:
                row = {field: getattr(record, field) for field in fieldnames}
                writer.writerow(row)
        
        print(f"✅ 已导出 {len(self.records)} 条记录到 {filename}")
    
    def export_to_json(self, filename: str):
        """导出到JSON文件"""
        if not self.records:
            print("❌ 没有记录可导出")
            return
        
        data = {
            'metadata': {
                'exported_at': datetime.now().isoformat(),
                'total_records': len(self.records),
                'database_path': self.db_path
            },
            'statistics': self.get_statistics(),
            'records': [asdict(record) for record in self.records]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 已导出 {len(self.records)} 条记录到 {filename}")
    
    def load_from_database(self, limit: int = None) -> List[TradeRecord]:
        """从数据库加载记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = "SELECT * FROM trade_records ORDER BY created_at DESC"
        if limit:
            query += f" LIMIT {limit}"
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        # 获取列名
        columns = [description[0] for description in cursor.description]
        
        records = []
        for row in rows:
            record_dict = dict(zip(columns, row))
            record = TradeRecord(**record_dict)
            records.append(record)
        
        conn.close()
        self.records = records
        return records
    
    def print_statistics(self):
        """打印统计信息"""
        stats = self.get_statistics()
        
        if not stats:
            print("❌ 没有交易记录")
            return
        
        print("📊 交易记录统计")
        print("=" * 50)
        print(f"总测试数: {stats['total_tests']}")
        print(f"成功交易: {stats['successful_tests']}")
        print(f"失败交易: {stats['failed_tests']}")
        print(f"成功率: {stats['success_rate']:.1f}%")
        print(f"总耗时: {stats['total_duration_seconds']:.1f} 秒")
        print(f"平均耗时: {stats['avg_duration_seconds']:.1f} 秒")
        print(f"平均等待时间: {stats['avg_wait_time_seconds']:.1f} 秒")
        print()
        
        print("🪙 币种分布:")
        for coin, data in stats['coin_distribution'].items():
            success_rate = data['success'] / data['total'] * 100 if data['total'] > 0 else 0
            print(f"  {coin}: {data['total']} 总数, {data['success']} 成功 ({success_rate:.1f}%)")
        print()
        
        print("🔍 测试类型分布:")
        for test_type, data in stats['test_type_distribution'].items():
            success_rate = data['success'] / data['total'] * 100 if data['total'] > 0 else 0
            type_name = "正常交易" if test_type == 'normal' else "疑似对敲"
            print(f"  {type_name}: {data['total']} 总数, {data['success']} 成功 ({success_rate:.1f}%)")


def main():
    """主函数 - 测试记录器"""
    print("📝 交易数据记录系统")
    print("=" * 40)
    
    recorder = TradeRecorder()
    
    while True:
        print("\n请选择:")
        print("1. 查看统计信息")
        print("2. 从数据库加载记录")
        print("3. 导出到CSV")
        print("4. 导出到JSON")
        print("5. 清空内存记录")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            recorder.print_statistics()
        elif choice == '2':
            try:
                limit = input("请输入加载数量限制 (留空加载全部): ").strip()
                limit = int(limit) if limit else None
                records = recorder.load_from_database(limit)
                print(f"✅ 已加载 {len(records)} 条记录")
                recorder.print_statistics()
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '3':
            filename = input("请输入CSV文件名 (默认: trade_records.csv): ").strip()
            if not filename:
                filename = "trade_records.csv"
            recorder.export_to_csv(filename)
        elif choice == '4':
            filename = input("请输入JSON文件名 (默认: trade_records.json): ").strip()
            if not filename:
                filename = "trade_records.json"
            recorder.export_to_json(filename)
        elif choice == '5':
            recorder.records = []
            print("✅ 已清空内存记录")
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
