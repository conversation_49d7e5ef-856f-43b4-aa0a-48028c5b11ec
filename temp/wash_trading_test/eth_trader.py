#!/usr/bin/env python3
"""
ETH交易工具
基于您提供的ETH请求格式和测试范围
"""

import sys
import os
import time
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'trading_bot'))

from utils.config import load_config
from utils.logger import setup_logger, get_logger
from api.client import BipcClient


def usdt_to_eth_orderqty(usdt_amount: float) -> int:
    """USDT金额转换为ETH orderQty
    
    根据您的信息，ETH最小下单是50U，需要确定具体的换算比例
    这里先使用1:1的比例，后续可以根据实际情况调整
    """
    # 临时使用1:1比例，需要根据实际测试调整
    return int(usdt_amount)


def eth_orderqty_to_usdt(orderqty: int) -> float:
    """ETH orderQty转换为USDT金额"""
    # 临时使用1:1比例，需要根据实际测试调整
    return float(orderqty)


def eth_trade_sequence(usdt_amount: float, wait_seconds: int = 10):
    """ETH交易序列：买入→等待指定时间→卖出
    
    Args:
        usdt_amount: USDT交易金额
        wait_seconds: 等待时间（秒）
    """
    print(f"🔷 ETH交易序列 ({usdt_amount} USDT)")
    print("=" * 50)
    print(f"流程: 买入ETH → 等待{wait_seconds}秒 → 卖出ETH")
    print("=" * 50)
    
    try:
        # 初始化
        config = load_config('config.yaml')
        setup_logger(config)
        logger = get_logger('ETHTrader')
        
        client = BipcClient(config)
        
        # 计算ETH数量
        eth_orderQty = usdt_to_eth_orderqty(usdt_amount)
        
        print(f"交易参数:")
        print(f"- USDT金额: {usdt_amount}")
        print(f"- ETH orderQty: {eth_orderQty}")
        print(f"- 等待时间: {wait_seconds}秒")
        
        # 买入ETH - 使用您提供的格式
        buy_order = {
            "symbol": "eth-usdt",
            "orderQty": eth_orderQty,
            "side": 2,              # 买入
            "type": "2",            # 市价单
            "source": 1
        }
        
        print(f"\n🔷 买入ETH...")
        print(f"订单: {buy_order}")
        logger.info(f"买入ETH订单: {buy_order}")
        
        # 执行买入
        url = client.endpoints.place_order
        buy_result = client._make_request('POST', url, data=buy_order, auth_required=True)
        
        print(f"买入结果: {json.dumps(buy_result, indent=2, ensure_ascii=False)}")
        logger.info(f"买入结果: {buy_result}")
        
        if buy_result.get('code') == 200:
            print("✅ 买入ETH成功！")
            
            # 提取订单ID
            buy_order_id = None
            if 'data' in buy_result and isinstance(buy_result['data'], dict):
                buy_order_id = buy_result['data'].get('orderId')
            
            print(f"📋 买入订单ID: {buy_order_id}")
            
            # 等待指定时间
            print(f"\n⏰ 等待{wait_seconds}秒...")
            for i in range(wait_seconds, 0, -1):
                print(f"   倒计时: {i} 秒", end='\r')
                time.sleep(1)
            print("\n")
            
            # 卖出ETH - 使用您提供的格式
            sell_order = {
                "symbol": "eth-usdt",
                "orderQty": eth_orderQty,
                "side": 1,              # 卖出
                "type": "2",            # 市价单
                "source": 1
            }
            
            print(f"🔷 卖出ETH...")
            print(f"订单: {sell_order}")
            logger.info(f"卖出ETH订单: {sell_order}")
            
            # 执行卖出
            sell_result = client._make_request('POST', url, data=sell_order, auth_required=True)
            
            print(f"卖出结果: {json.dumps(sell_result, indent=2, ensure_ascii=False)}")
            logger.info(f"卖出结果: {sell_result}")
            
            if sell_result.get('code') == 200:
                print("✅ 卖出ETH成功！")
                
                # 提取订单ID
                sell_order_id = None
                if 'data' in sell_result and isinstance(sell_result['data'], dict):
                    sell_order_id = sell_result['data'].get('orderId')
                
                print(f"📋 卖出订单ID: {sell_order_id}")
                
                # 交易完成
                print(f"\n🎉 ETH交易序列完成！")
                print("=" * 40)
                print(f"买入订单ID: {buy_order_id}")
                print(f"卖出订单ID: {sell_order_id}")
                print(f"交易orderQty: {eth_orderQty}")
                print(f"交易金额: {usdt_amount} USDT")
                print(f"总耗时: 约{wait_seconds}秒")
                
                logger.info(f"ETH交易完成: 买入{buy_order_id}, 卖出{sell_order_id}")
                
                return {
                    'success': True,
                    'buy_order_id': buy_order_id,
                    'sell_order_id': sell_order_id,
                    'orderQty': eth_orderQty,
                    'usdt_amount': usdt_amount,
                    'wait_seconds': wait_seconds
                }
                
            else:
                print(f"❌ 卖出ETH失败: {sell_result.get('message')}")
                logger.error(f"卖出失败: {sell_result}")
                return {'success': False, 'error': 'sell_failed', 'message': sell_result.get('message')}
                
        else:
            print(f"❌ 买入ETH失败: {buy_result.get('message')}")
            logger.error(f"买入失败: {buy_result}")
            return {'success': False, 'error': 'buy_failed', 'message': buy_result.get('message')}
            
    except Exception as e:
        print(f"❌ ETH交易失败: {e}")
        logger.error(f"ETH交易失败: {e}")
        return {'success': False, 'error': 'exception', 'message': str(e)}


def eth_close_position(orderqty: int, side: int):
    """ETH平仓操作"""
    print(f"📉 ETH平仓操作")
    print("=" * 30)
    print(f"数量: {orderqty} orderQty")
    print(f"方向: {'平多仓' if side == 1 else '平空仓' if side == 2 else '未知'}")
    
    try:
        config = load_config('config.yaml')
        client = BipcClient(config)
        
        # 平仓订单
        close_order = {
            "symbol": "eth-usdt",
            "orderQty": orderqty,
            "side": side,
            "type": "5",            # 平仓
            "source": 1,
            "reduceOnly": 1         # 平仓标识
        }
        
        print(f"平仓订单: {close_order}")
        
        # 执行平仓
        url = client.endpoints.place_order
        result = client._make_request('POST', url, data=close_order, auth_required=True)
        
        print(f"平仓结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('code') == 200:
            print("✅ ETH平仓成功！")
            order_id = None
            if 'data' in result and isinstance(result['data'], dict):
                order_id = result['data'].get('orderId')
            print(f"📋 平仓订单ID: {order_id}")
            return {'success': True, 'order_id': order_id}
        else:
            print(f"❌ ETH平仓失败: {result.get('message')}")
            return {'success': False, 'message': result.get('message')}
            
    except Exception as e:
        print(f"❌ ETH平仓异常: {e}")
        return {'success': False, 'message': str(e)}


def show_eth_conversion_table():
    """显示ETH换算表"""
    print("📊 ETH-USDT 数量换算表")
    print("=" * 40)
    print("当前使用临时比例: 1 USDT = 1 orderQty")
    print("⚠️  需要根据实际测试调整换算比例")
    print("=" * 40)
    
    # ETH测试范围的USDT金额
    usdt_amounts = [50, 100, 200, 500, 1000, 2000, 3000, 5000]
    
    print("USDT金额 → orderQty")
    print("-" * 25)
    for usdt in usdt_amounts:
        orderqty = usdt_to_eth_orderqty(usdt)
        print(f"{usdt:>8} USDT → {orderqty:>4} orderQty")


def main():
    """主函数"""
    print("🔷 ETH交易工具")
    print("=" * 40)
    print("测试范围: 50-5000 USDT")
    print("最小下单: 50 USDT")
    print("=" * 40)
    
    while True:
        print("\n请选择:")
        print("1. ETH交易序列（自定义金额和时间）")
        print("2. 快速ETH交易（50 USDT，10秒）")
        print("3. 快速ETH交易（100 USDT，10秒）")
        print("4. 快速ETH交易（500 USDT，10秒）")
        print("5. ETH平仓（平多仓）")
        print("6. ETH平仓（平空仓）")
        print("7. 显示ETH换算表")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-7): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            try:
                amount = float(input("请输入USDT金额 (50-5000): "))
                if 50 <= amount <= 5000:
                    wait_time = int(input("请输入等待时间（秒，1-120）: "))
                    if 1 <= wait_time <= 120:
                        eth_trade_sequence(amount, wait_time)
                    else:
                        print("❌ 等待时间必须在1-120秒之间")
                else:
                    print("❌ 金额必须在50-5000 USDT之间")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '2':
            eth_trade_sequence(50, 10)
        elif choice == '3':
            eth_trade_sequence(100, 10)
        elif choice == '4':
            eth_trade_sequence(500, 10)
        elif choice == '5':
            try:
                orderqty = int(input("请输入平仓数量(orderQty): "))
                eth_close_position(orderqty, 1)  # 平多仓
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '6':
            try:
                orderqty = int(input("请输入平仓数量(orderQty): "))
                eth_close_position(orderqty, 2)  # 平空仓
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '7':
            show_eth_conversion_table()
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
