#!/usr/bin/env python3
"""
统一交易接口
支持BTC、ETH、DOG三个币种的统一交易操作
"""

import sys
import os
import time
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'trading_bot'))

from utils.config import load_config
from utils.logger import setup_logger, get_logger
from api.client import BipcClient


@dataclass
class CoinConfig:
    """币种配置"""
    symbol: str          # 交易对符号
    min_usdt: float      # 最小交易金额(USDT)
    max_usdt: float      # 最大交易金额(USDT)
    usdt_to_orderqty_ratio: float  # USDT到orderQty的换算比例
    name: str            # 币种名称
    emoji: str           # 币种表情符号


class UnifiedTrader:
    """统一交易器"""
    
    # 币种配置
    COIN_CONFIGS = {
        'DOG': CoinConfig(
            symbol='doge-usdt',
            min_usdt=1.0,
            max_usdt=100.0,
            usdt_to_orderqty_ratio=4.0,  # 1 USDT = 4 orderQty
            name='DOGE',
            emoji='🐕'
        ),
        'ETH': CoinConfig(
            symbol='eth-usdt',
            min_usdt=50.0,
            max_usdt=5000.0,
            usdt_to_orderqty_ratio=1.0,  # 1 USDT = 1 orderQty (临时，需调整)
            name='ETH',
            emoji='🔷'
        ),
        'BTC': CoinConfig(
            symbol='btc-usdt',
            min_usdt=5000.0,
            max_usdt=100000.0,
            usdt_to_orderqty_ratio=86.0/10000.0,  # 10000 USDT = 86 orderQty
            name='BTC',
            emoji='₿'
        )
    }
    
    def __init__(self):
        """初始化统一交易器"""
        self.config = load_config('config.yaml')
        setup_logger(self.config)
        self.logger = get_logger('UnifiedTrader')
        self.client = BipcClient(self.config)
    
    def usdt_to_orderqty(self, coin: str, usdt_amount: float) -> int:
        """USDT金额转换为orderQty"""
        if coin not in self.COIN_CONFIGS:
            raise ValueError(f"不支持的币种: {coin}")
        
        config = self.COIN_CONFIGS[coin]
        return int(usdt_amount * config.usdt_to_orderqty_ratio)
    
    def orderqty_to_usdt(self, coin: str, orderqty: int) -> float:
        """orderQty转换为USDT金额"""
        if coin not in self.COIN_CONFIGS:
            raise ValueError(f"不支持的币种: {coin}")
        
        config = self.COIN_CONFIGS[coin]
        return orderqty / config.usdt_to_orderqty_ratio
    
    def validate_amount(self, coin: str, usdt_amount: float) -> bool:
        """验证交易金额是否在允许范围内"""
        if coin not in self.COIN_CONFIGS:
            return False
        
        config = self.COIN_CONFIGS[coin]
        return config.min_usdt <= usdt_amount <= config.max_usdt
    
    def place_order(self, coin: str, usdt_amount: float, side: int, order_type: str = "2") -> Dict[str, Any]:
        """下单
        
        Args:
            coin: 币种 ('BTC', 'ETH', 'DOG')
            usdt_amount: USDT交易金额
            side: 交易方向 (1=卖出, 2=买入)
            order_type: 订单类型 ("2"=市价单)
            
        Returns:
            下单结果
        """
        if coin not in self.COIN_CONFIGS:
            return {'success': False, 'error': 'unsupported_coin', 'message': f'不支持的币种: {coin}'}
        
        if not self.validate_amount(coin, usdt_amount):
            config = self.COIN_CONFIGS[coin]
            return {
                'success': False, 
                'error': 'invalid_amount', 
                'message': f'{coin}交易金额必须在{config.min_usdt}-{config.max_usdt} USDT之间'
            }
        
        try:
            config = self.COIN_CONFIGS[coin]
            orderqty = self.usdt_to_orderqty(coin, usdt_amount)
            
            order_data = {
                "symbol": config.symbol,
                "orderQty": orderqty,
                "side": side,
                "type": order_type,
                "source": 1
            }
            
            self.logger.info(f"{coin}下单: {order_data}")
            
            url = self.client.endpoints.place_order
            result = self.client._make_request('POST', url, data=order_data, auth_required=True)
            
            if result.get('code') == 200:
                order_id = None
                if 'data' in result and isinstance(result['data'], dict):
                    order_id = result['data'].get('orderId')
                
                return {
                    'success': True,
                    'order_id': order_id,
                    'coin': coin,
                    'usdt_amount': usdt_amount,
                    'orderqty': orderqty,
                    'side': side,
                    'timestamp': datetime.now().isoformat(),
                    'raw_result': result
                }
            else:
                return {
                    'success': False,
                    'error': 'api_error',
                    'message': result.get('message', '未知错误'),
                    'raw_result': result
                }
                
        except Exception as e:
            self.logger.error(f"{coin}下单异常: {e}")
            return {
                'success': False,
                'error': 'exception',
                'message': str(e)
            }
    
    def buy(self, coin: str, usdt_amount: float) -> Dict[str, Any]:
        """买入"""
        return self.place_order(coin, usdt_amount, side=2)
    
    def sell(self, coin: str, usdt_amount: float) -> Dict[str, Any]:
        """卖出"""
        return self.place_order(coin, usdt_amount, side=1)
    
    def trade_sequence(self, coin: str, usdt_amount: float, wait_seconds: int = 10) -> Dict[str, Any]:
        """完整交易序列：买入→等待→卖出
        
        Args:
            coin: 币种
            usdt_amount: 交易金额
            wait_seconds: 等待时间
            
        Returns:
            交易结果
        """
        if coin not in self.COIN_CONFIGS:
            return {'success': False, 'error': 'unsupported_coin'}
        
        config = self.COIN_CONFIGS[coin]
        start_time = datetime.now()
        
        self.logger.info(f"开始{coin}交易序列: {usdt_amount} USDT, 等待{wait_seconds}秒")
        
        # 买入
        buy_result = self.buy(coin, usdt_amount)
        if not buy_result['success']:
            return {
                'success': False,
                'error': 'buy_failed',
                'buy_result': buy_result,
                'coin': coin,
                'usdt_amount': usdt_amount
            }
        
        buy_time = datetime.now()
        
        # 等待
        time.sleep(wait_seconds)
        
        # 卖出
        sell_result = self.sell(coin, usdt_amount)
        if not sell_result['success']:
            return {
                'success': False,
                'error': 'sell_failed',
                'buy_result': buy_result,
                'sell_result': sell_result,
                'coin': coin,
                'usdt_amount': usdt_amount
            }
        
        sell_time = datetime.now()
        end_time = datetime.now()
        
        # 计算时间
        total_duration = (end_time - start_time).total_seconds()
        actual_wait_time = (sell_time - buy_time).total_seconds()
        
        return {
            'success': True,
            'coin': coin,
            'usdt_amount': usdt_amount,
            'orderqty': buy_result['orderqty'],
            'buy_order_id': buy_result['order_id'],
            'sell_order_id': sell_result['order_id'],
            'start_time': start_time.isoformat(),
            'buy_time': buy_time.isoformat(),
            'sell_time': sell_time.isoformat(),
            'end_time': end_time.isoformat(),
            'planned_wait_seconds': wait_seconds,
            'actual_wait_seconds': actual_wait_time,
            'total_duration_seconds': total_duration,
            'buy_result': buy_result,
            'sell_result': sell_result
        }
    
    def get_coin_info(self, coin: str) -> Optional[CoinConfig]:
        """获取币种信息"""
        return self.COIN_CONFIGS.get(coin)
    
    def get_supported_coins(self) -> list:
        """获取支持的币种列表"""
        return list(self.COIN_CONFIGS.keys())
    
    def show_coin_ranges(self):
        """显示各币种的交易范围"""
        print("📊 支持的币种和交易范围")
        print("=" * 50)
        
        for coin, config in self.COIN_CONFIGS.items():
            print(f"{config.emoji} {config.name} ({config.symbol})")
            print(f"   交易范围: {config.min_usdt}-{config.max_usdt} USDT")
            print(f"   换算比例: 1 USDT = {config.usdt_to_orderqty_ratio} orderQty")
            print()


def main():
    """主函数 - 测试统一交易接口"""
    trader = UnifiedTrader()
    
    print("🔄 统一交易接口测试")
    print("=" * 40)
    
    trader.show_coin_ranges()
    
    while True:
        print("\n请选择测试:")
        print("1. DOG交易测试 (1-100 USDT)")
        print("2. ETH交易测试 (50-5000 USDT)")
        print("3. BTC交易测试 (5000-100000 USDT)")
        print("4. 自定义交易测试")
        print("5. 显示币种信息")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            try:
                amount = float(input("请输入DOG交易金额 (1-100 USDT): "))
                wait_time = int(input("请输入等待时间 (1-120秒): "))
                result = trader.trade_sequence('DOG', amount, wait_time)
                print(f"交易结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '2':
            try:
                amount = float(input("请输入ETH交易金额 (50-5000 USDT): "))
                wait_time = int(input("请输入等待时间 (1-120秒): "))
                result = trader.trade_sequence('ETH', amount, wait_time)
                print(f"交易结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '3':
            try:
                amount = float(input("请输入BTC交易金额 (5000-100000 USDT): "))
                wait_time = int(input("请输入等待时间 (1-120秒): "))
                result = trader.trade_sequence('BTC', amount, wait_time)
                print(f"交易结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '4':
            try:
                coin = input("请输入币种 (DOG/ETH/BTC): ").upper()
                amount = float(input("请输入交易金额 (USDT): "))
                wait_time = int(input("请输入等待时间 (1-120秒): "))
                result = trader.trade_sequence(coin, amount, wait_time)
                print(f"交易结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '5':
            trader.show_coin_ranges()
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
