#!/usr/bin/env python3
"""
对敲检测测试系统配置文件
"""

# 测试配置
TEST_CONFIG = {
    # 默认测试参数
    'default_test_count': 5000,
    'default_delay_between_tests': 0.5,  # 秒
    'max_concurrent_tests': 3,
    
    # 币种分布（默认）
    'default_coin_distribution': {
        'DOG': 0.4,  # 40%
        'ETH': 0.4,  # 40%
        'BTC': 0.2   # 20%
    },
    
    # 时间窗口配置
    'time_windows': {
        'default': 30.0,  # 默认30秒
        'strict': 15.0,   # 严格15秒
        'loose': 60.0     # 宽松60秒
    }
}

# 对敲检测配置
WASH_TRADING_CONFIG = {
    # 评分权重
    'scoring_weights': {
        'profit_hedge': 0.4,    # 盈亏对敲权重
        'time_match': 0.25,     # 时间匹配权重
        'amount_match': 0.25,   # 金额匹配权重
        'duration': 0.1         # 持仓时长权重
    },
    
    # 风险阈值
    'risk_thresholds': {
        'wash_score_threshold': 0.7,
        'profit_hedge_threshold': 0.7,
        'confidence_threshold': 0.6
    },
    
    # 风险等级配置
    'risk_levels': {
        'critical': {
            'wash_score_min': 0.9,
            'profit_hedge_min': 0.9,
            'time_match_min': 0.8
        },
        'high': {
            'wash_score_min': 0.85
        },
        'medium': {
            'wash_score_min': 0.7
        },
        'low': {
            'wash_score_min': 0.5
        }
    }
}

# 币种配置
COIN_CONFIG = {
    'DOG': {
        'symbol': 'doge-usdt',
        'min_usdt': 1.0,
        'max_usdt': 100.0,
        'usdt_to_orderqty_ratio': 4.0,
        'name': 'DOGE',
        'emoji': '🐕',
        'test_amounts': [1, 5, 10, 25, 50, 100]  # 推荐测试金额
    },
    'ETH': {
        'symbol': 'eth-usdt',
        'min_usdt': 50.0,
        'max_usdt': 5000.0,
        'usdt_to_orderqty_ratio': 1.0,  # 需要根据实际情况调整
        'name': 'ETH',
        'emoji': '🔷',
        'test_amounts': [50, 100, 200, 500, 1000, 2000, 5000]
    },
    'BTC': {
        'symbol': 'btc-usdt',
        'min_usdt': 5000.0,
        'max_usdt': 100000.0,
        'usdt_to_orderqty_ratio': 86.0/10000.0,
        'name': 'BTC',
        'emoji': '₿',
        'test_amounts': [5000, 10000, 20000, 50000, 100000]
    }
}

# 数据库配置
DATABASE_CONFIG = {
    'db_path': 'wash_trading_test.db',
    'backup_interval_hours': 24,
    'max_records_per_query': 10000
}

# 导出配置
EXPORT_CONFIG = {
    'default_formats': ['json', 'csv'],
    'timestamp_format': '%Y%m%d_%H%M%S',
    'file_prefixes': {
        'trade_records': 'trade_records',
        'analysis_results': 'wash_trading_analysis',
        'execution_report': 'test_execution_report'
    }
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d | %(message)s',
    'file_rotation': True,
    'max_file_size_mb': 100
}

# 安全配置
SECURITY_CONFIG = {
    'max_single_test_amount': 100000,  # 单次测试最大金额
    'daily_test_limit': 10000,         # 每日测试限制
    'require_confirmation_above': 1000, # 超过此金额需要确认
    'emergency_stop_enabled': True     # 紧急停止功能
}

# API配置
API_CONFIG = {
    'request_timeout': 30,
    'max_retries': 3,
    'retry_delay': 1.0,
    'rate_limit_delay': 0.1
}

def get_config(section: str = None):
    """获取配置
    
    Args:
        section: 配置节名称，如果为None则返回所有配置
        
    Returns:
        配置字典
    """
    all_configs = {
        'test': TEST_CONFIG,
        'wash_trading': WASH_TRADING_CONFIG,
        'coin': COIN_CONFIG,
        'database': DATABASE_CONFIG,
        'export': EXPORT_CONFIG,
        'logging': LOGGING_CONFIG,
        'security': SECURITY_CONFIG,
        'api': API_CONFIG
    }
    
    if section is None:
        return all_configs
    
    return all_configs.get(section, {})

def validate_config():
    """验证配置有效性"""
    errors = []
    
    # 验证币种分布总和
    coin_dist = TEST_CONFIG['default_coin_distribution']
    if abs(sum(coin_dist.values()) - 1.0) > 0.01:
        errors.append("币种分布总和必须为1.0")
    
    # 验证评分权重总和
    weights = WASH_TRADING_CONFIG['scoring_weights']
    if abs(sum(weights.values()) - 1.0) > 0.01:
        errors.append("评分权重总和必须为1.0")
    
    # 验证币种配置
    for coin, config in COIN_CONFIG.items():
        if config['min_usdt'] >= config['max_usdt']:
            errors.append(f"{coin}币种的最小金额必须小于最大金额")
    
    return errors

def print_config_summary():
    """打印配置摘要"""
    print("⚙️  系统配置摘要")
    print("=" * 50)
    
    print("📊 测试配置:")
    print(f"  默认测试数量: {TEST_CONFIG['default_test_count']}")
    print(f"  测试间隔: {TEST_CONFIG['default_delay_between_tests']}秒")
    print(f"  最大并发: {TEST_CONFIG['max_concurrent_tests']}")
    
    print("\n🪙 币种配置:")
    for coin, config in COIN_CONFIG.items():
        print(f"  {config['emoji']} {coin}: {config['min_usdt']}-{config['max_usdt']} USDT")
    
    print("\n🔍 对敲检测配置:")
    weights = WASH_TRADING_CONFIG['scoring_weights']
    print(f"  盈亏对敲权重: {weights['profit_hedge']*100}%")
    print(f"  时间匹配权重: {weights['time_match']*100}%")
    print(f"  金额匹配权重: {weights['amount_match']*100}%")
    
    print("\n🛡️  安全配置:")
    security = SECURITY_CONFIG
    print(f"  单次测试最大金额: {security['max_single_test_amount']} USDT")
    print(f"  每日测试限制: {security['daily_test_limit']}")
    print(f"  需要确认的金额阈值: {security['require_confirmation_above']} USDT")
    
    # 验证配置
    errors = validate_config()
    if errors:
        print("\n❌ 配置错误:")
        for error in errors:
            print(f"  • {error}")
    else:
        print("\n✅ 配置验证通过")

if __name__ == "__main__":
    print_config_summary()
