#!/usr/bin/env python3
"""
BTC交易工具
基于现有的BTC交易模块，适配对敲检测测试系统
"""

import sys
import os
import time
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'trading_bot'))

from utils.config import load_config
from utils.logger import setup_logger, get_logger
from api.client import BipcClient


def usdt_to_btc_orderqty(usdt_amount: float) -> int:
    """USDT金额转换为BTC orderQty
    
    基于真实数据：10000 USDT = 86 orderQty
    """
    return int(usdt_amount * 86 / 10000)


def btc_orderqty_to_usdt(orderqty: int) -> float:
    """BTC orderQty转换为USDT金额"""
    return orderqty * 10000 / 86


def btc_trade_sequence(usdt_amount: float, wait_seconds: int = 10):
    """BTC交易序列：买入→等待指定时间→卖出
    
    Args:
        usdt_amount: USDT交易金额
        wait_seconds: 等待时间（秒）
    """
    print(f"₿ BTC交易序列 ({usdt_amount} USDT)")
    print("=" * 50)
    print(f"流程: 买入BTC → 等待{wait_seconds}秒 → 卖出BTC")
    print("=" * 50)
    
    try:
        # 初始化
        config = load_config('config.yaml')
        setup_logger(config)
        logger = get_logger('BTCTrader')
        
        client = BipcClient(config)
        
        # 计算BTC数量
        btc_orderQty = usdt_to_btc_orderqty(usdt_amount)
        
        print(f"交易参数:")
        print(f"- USDT金额: {usdt_amount}")
        print(f"- BTC orderQty: {btc_orderQty}")
        print(f"- 换算比例: 10000 USDT = 86 orderQty")
        print(f"- 等待时间: {wait_seconds}秒")
        
        # 买入BTC
        buy_order = {
            "symbol": "btc-usdt",
            "orderQty": btc_orderQty,
            "side": 2,              # 买入
            "type": "2",            # 市价单
            "source": 1,
            "ppw": "W001"
        }
        
        print(f"\n₿ 买入BTC...")
        print(f"订单: {buy_order}")
        logger.info(f"买入BTC订单: {buy_order}")
        
        # 执行买入
        url = client.endpoints.place_order
        buy_result = client._make_request('POST', url, data=buy_order, auth_required=True)
        
        print(f"买入结果: {json.dumps(buy_result, indent=2, ensure_ascii=False)}")
        logger.info(f"买入结果: {buy_result}")
        
        if buy_result.get('code') == 200:
            print("✅ 买入BTC成功！")
            
            # 提取订单ID
            buy_order_id = None
            if 'data' in buy_result and isinstance(buy_result['data'], dict):
                buy_order_id = buy_result['data'].get('orderId')
            
            print(f"📋 买入订单ID: {buy_order_id}")
            
            # 等待指定时间
            print(f"\n⏰ 等待{wait_seconds}秒...")
            for i in range(wait_seconds, 0, -1):
                print(f"   倒计时: {i} 秒", end='\r')
                time.sleep(1)
            print("\n")
            
            # 卖出BTC
            sell_order = {
                "symbol": "btc-usdt",
                "orderQty": btc_orderQty,
                "side": 1,              # 卖出
                "type": "2",            # 市价单
                "source": 1,
                "ppw": "W001"
            }
            
            print(f"₿ 卖出BTC...")
            print(f"订单: {sell_order}")
            logger.info(f"卖出BTC订单: {sell_order}")
            
            # 执行卖出
            sell_result = client._make_request('POST', url, data=sell_order, auth_required=True)
            
            print(f"卖出结果: {json.dumps(sell_result, indent=2, ensure_ascii=False)}")
            logger.info(f"卖出结果: {sell_result}")
            
            if sell_result.get('code') == 200:
                print("✅ 卖出BTC成功！")
                
                # 提取订单ID
                sell_order_id = None
                if 'data' in sell_result and isinstance(sell_result['data'], dict):
                    sell_order_id = sell_result['data'].get('orderId')
                
                print(f"📋 卖出订单ID: {sell_order_id}")
                
                # 交易完成
                print(f"\n🎉 BTC交易序列完成！")
                print("=" * 40)
                print(f"买入订单ID: {buy_order_id}")
                print(f"卖出订单ID: {sell_order_id}")
                print(f"交易orderQty: {btc_orderQty}")
                print(f"交易金额: {usdt_amount} USDT")
                print(f"总耗时: 约{wait_seconds}秒")
                
                logger.info(f"BTC交易完成: 买入{buy_order_id}, 卖出{sell_order_id}")
                
                return {
                    'success': True,
                    'buy_order_id': buy_order_id,
                    'sell_order_id': sell_order_id,
                    'orderQty': btc_orderQty,
                    'usdt_amount': usdt_amount,
                    'wait_seconds': wait_seconds
                }
                
            else:
                print(f"❌ 卖出BTC失败: {sell_result.get('message')}")
                logger.error(f"卖出失败: {sell_result}")
                return {'success': False, 'error': 'sell_failed', 'message': sell_result.get('message')}
                
        else:
            print(f"❌ 买入BTC失败: {buy_result.get('message')}")
            logger.error(f"买入失败: {buy_result}")
            return {'success': False, 'error': 'buy_failed', 'message': buy_result.get('message')}
            
    except Exception as e:
        print(f"❌ BTC交易失败: {e}")
        logger.error(f"BTC交易失败: {e}")
        return {'success': False, 'error': 'exception', 'message': str(e)}


def btc_close_position(orderqty: int, side: int):
    """BTC平仓操作"""
    print(f"📉 BTC平仓操作")
    print("=" * 30)
    print(f"数量: {orderqty} orderQty")
    print(f"方向: {'平多仓' if side == 1 else '平空仓' if side == 2 else '未知'}")
    
    try:
        config = load_config('config.yaml')
        client = BipcClient(config)
        
        # 平仓订单
        close_order = {
            "symbol": "btc-usdt",
            "orderQty": orderqty,
            "side": side,
            "type": "5",            # 平仓
            "source": 1,
            "reduceOnly": 1,        # 平仓标识
            "ppw": "W001"
        }
        
        print(f"平仓订单: {close_order}")
        
        # 执行平仓
        url = client.endpoints.place_order
        result = client._make_request('POST', url, data=close_order, auth_required=True)
        
        print(f"平仓结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('code') == 200:
            print("✅ BTC平仓成功！")
            order_id = None
            if 'data' in result and isinstance(result['data'], dict):
                order_id = result['data'].get('orderId')
            print(f"📋 平仓订单ID: {order_id}")
            return {'success': True, 'order_id': order_id}
        else:
            print(f"❌ BTC平仓失败: {result.get('message')}")
            return {'success': False, 'message': result.get('message')}
            
    except Exception as e:
        print(f"❌ BTC平仓异常: {e}")
        return {'success': False, 'message': str(e)}


def show_btc_conversion_table():
    """显示BTC换算表"""
    print("📊 BTC-USDT 数量换算表")
    print("=" * 40)
    print("基于真实数据: 10000 USDT = 86 orderQty")
    print("=" * 40)
    
    # BTC测试范围的USDT金额
    usdt_amounts = [5000, 10000, 20000, 30000, 50000, 80000, 100000]
    
    print("USDT金额 → orderQty")
    print("-" * 25)
    for usdt in usdt_amounts:
        orderqty = usdt_to_btc_orderqty(usdt)
        print(f"{usdt:>8} USDT → {orderqty:>4} orderQty")
    
    print("\norderQty → USDT金额")
    print("-" * 25)
    orderqty_amounts = [43, 86, 172, 258, 430, 688, 860]
    for orderqty in orderqty_amounts:
        usdt = btc_orderqty_to_usdt(orderqty)
        print(f"{orderqty:>4} orderQty → {usdt:>8.0f} USDT")


def main():
    """主函数"""
    print("₿ BTC交易工具")
    print("=" * 40)
    print("测试范围: 5000-100000 USDT")
    print("最小下单: 118 USDT (约1 orderQty)")
    print("换算比例: 10000 USDT = 86 orderQty")
    print("=" * 40)
    
    while True:
        print("\n请选择:")
        print("1. BTC交易序列（自定义金额和时间）")
        print("2. 快速BTC交易（5000 USDT，10秒）")
        print("3. 快速BTC交易（10000 USDT，10秒）")
        print("4. 快速BTC交易（50000 USDT，10秒）")
        print("5. BTC平仓（平多仓）")
        print("6. BTC平仓（平空仓）")
        print("7. 显示BTC换算表")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-7): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            try:
                amount = float(input("请输入USDT金额 (5000-100000): "))
                if 5000 <= amount <= 100000:
                    wait_time = int(input("请输入等待时间（秒，1-120）: "))
                    if 1 <= wait_time <= 120:
                        btc_trade_sequence(amount, wait_time)
                    else:
                        print("❌ 等待时间必须在1-120秒之间")
                else:
                    print("❌ 金额必须在5000-100000 USDT之间")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '2':
            btc_trade_sequence(5000, 10)
        elif choice == '3':
            btc_trade_sequence(10000, 10)
        elif choice == '4':
            btc_trade_sequence(50000, 10)
        elif choice == '5':
            try:
                orderqty = int(input("请输入平仓数量(orderQty): "))
                btc_close_position(orderqty, 1)  # 平多仓
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '6':
            try:
                orderqty = int(input("请输入平仓数量(orderQty): "))
                btc_close_position(orderqty, 2)  # 平空仓
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '7':
            show_btc_conversion_table()
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
