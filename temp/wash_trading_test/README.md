# 对敲检测测试系统

基于算法文档要求开发的完整对敲检测测试系统，支持BTC、ETH、DOG三个币种的自动化交易测试和数据分析。

## 系统概述

本系统用于测试对敲检测算法的有效性，通过模拟真实交易场景，生成符合对敲特征的测试数据，并进行深度分析。

### 核心功能

- **多币种支持**: BTC、ETH、DOG三个币种
- **智能参数生成**: 10U-10万U金额范围，1-120秒时间间隔
- **自动化执行**: 支持5000组测试的批量执行
- **实时数据记录**: 完整记录交易过程和结果
- **对敲检测分析**: 基于算法文档的评分机制
- **报告生成**: 多格式数据导出和统计报告

### 币种测试范围

| 币种 | 最小金额 | 最大金额 | 测试范围 | 换算比例 |
|------|----------|----------|----------|----------|
| DOG  | 1 USDT   | 100 USDT | 1-100U   | 1:4      |
| ETH  | 50 USDT  | 5000 USDT| 50-5000U | 1:1      |
| BTC  | 5000 USDT| 100000 USDT| 5000-10万U| 10000:86 |

## 系统架构

```
对敲检测测试系统
├── main.py                    # 主程序入口
├── test_data_generator.py     # 测试数据生成器
├── unified_trader.py          # 统一交易接口
├── eth_trader.py             # ETH交易模块
├── trade_recorder.py         # 交易数据记录系统
├── test_execution_engine.py  # 测试执行引擎
├── data_analyzer.py          # 数据分析和报告
└── README.md                 # 系统说明文档
```

## 快速开始

### 1. 环境准备

确保已安装必要的依赖：
```bash
# 进入trading_bot目录
cd trading_bot

# 检查配置文件
cat config.yaml
```

### 2. 运行系统

```bash
# 进入测试系统目录
cd temp/wash_trading_test

# 运行主程序
python main.py
```

### 3. 系统菜单

```
🎯 对敲检测测试系统
========================================
1. 查看系统状态
2. 运行快速测试 (5个测试)
3. 运行完整测试套件 (5000个测试)
4. 运行自定义测试
5. 分析测试结果
6. 查看测试记录
7. 生成测试用例预览
0. 退出
```

## 模块详解

### 1. 测试数据生成器 (test_data_generator.py)

**功能**: 生成符合对敲检测要求的测试参数

**特点**:
- 智能金额分布：使用对数分布让小额交易更常见
- 加权时间间隔：短时间间隔更常见（对敲特征）
- 自动分类：根据参数判断是否为疑似对敲
- 统计分析：提供详细的分布统计

**使用示例**:
```python
from test_data_generator import WashTradingTestGenerator

generator = WashTradingTestGenerator()
test_cases = generator.generate_test_batch(5000)
generator.print_statistics(test_cases)
```

### 2. 统一交易接口 (unified_trader.py)

**功能**: 提供三个币种的统一交易操作

**特点**:
- 统一API：所有币种使用相同的接口
- 自动换算：USDT金额到orderQty的自动转换
- 参数验证：交易金额范围验证
- 完整序列：买入→等待→卖出的完整流程

**使用示例**:
```python
from unified_trader import UnifiedTrader

trader = UnifiedTrader()
result = trader.trade_sequence('ETH', 100, 10)  # ETH, 100 USDT, 等待10秒
```

### 3. 交易数据记录系统 (trade_recorder.py)

**功能**: 记录和管理所有交易数据

**特点**:
- SQLite数据库：持久化存储
- 完整记录：包含时间、金额、订单ID等
- 统计分析：实时统计成功率、分布等
- 多格式导出：支持JSON、CSV格式

**数据库表结构**:
```sql
CREATE TABLE trade_records (
    record_id TEXT PRIMARY KEY,
    test_case_id INTEGER,
    coin TEXT,
    usdt_amount REAL,
    buy_order_id TEXT,
    sell_order_id TEXT,
    trade_success BOOLEAN,
    -- ... 更多字段
);
```

### 4. 测试执行引擎 (test_execution_engine.py)

**功能**: 自动化执行大批量测试

**特点**:
- 批量执行：支持5000组测试的自动执行
- 进度监控：实时显示执行进度和统计
- 错误处理：完善的异常处理和恢复
- 中断支持：支持用户中断和恢复

**执行流程**:
1. 生成测试用例
2. 逐个执行交易序列
3. 记录交易结果
4. 更新统计信息
5. 生成执行报告

### 5. 数据分析器 (data_analyzer.py)

**功能**: 基于算法文档的对敲检测分析

**分析维度**:
- **时间匹配分数**: 使用指数衰减函数
- **金额匹配分数**: 阶梯式容差计算
- **盈亏对敲分数**: 盈亏抵消程度评估
- **综合对敲分数**: 加权综合评分

**风险等级**:
- Critical: 极高风险
- High: 高风险  
- Medium: 中风险
- Low: 低风险
- Minimal: 极低风险

## 对敲检测算法

### 评分机制

根据算法文档实现的评分机制：

```python
# 综合对敲分数计算
wash_score = (
    profit_hedge_score * 0.4 +      # 盈亏对敲权重40%
    time_match_score * 0.25 +       # 时间匹配权重25%
    amount_match_score * 0.25 +     # 金额匹配权重25%
    duration_similarity * 0.1       # 持仓时长权重10%
)
```

### 判定条件

- **主要条件**: wash_score > 0.7
- **核心条件**: profit_hedge_score > 0.7
- **最终判定**: 同时满足主要条件和核心条件

## 数据输出

### 1. 交易记录 (CSV/JSON)

包含每笔交易的详细信息：
- 交易ID、币种、金额
- 买入/卖出时间和订单ID
- 交易成功状态和错误信息
- 计划/实际等待时间

### 2. 分析报告 (JSON)

包含对敲检测分析结果：
- 总体统计信息
- 风险等级分布
- 币种分布统计
- 分数统计分析
- 时间分布分析
- 疑似对敲交易对列表

### 3. 执行报告 (JSON)

包含测试执行摘要：
- 测试会话信息
- 成功/失败统计
- 执行时间统计
- 错误信息汇总

## 注意事项

### ⚠️ 风险提示

1. **真实交易**: 本系统执行真实交易，会产生实际费用
2. **市场影响**: 大量交易可能对市场产生影响
3. **资金安全**: 请确保账户资金充足且安全
4. **测试环境**: 建议先在测试环境验证

### 🔧 配置要求

1. **API配置**: 确保trading_bot/config.yaml配置正确
2. **网络连接**: 稳定的网络连接
3. **存储空间**: 足够的磁盘空间存储数据
4. **执行时间**: 5000个测试预计需要2-4小时

### 📊 性能优化

1. **并发控制**: 避免过高的并发请求
2. **延迟设置**: 适当的测试间隔避免限流
3. **错误重试**: 自动重试机制处理临时错误
4. **数据清理**: 定期清理历史数据

## 技术支持

如有问题或建议，请联系开发团队。

---

**版本**: 1.0  
**更新时间**: 2025-08-01  
**开发团队**: Augment Agent
