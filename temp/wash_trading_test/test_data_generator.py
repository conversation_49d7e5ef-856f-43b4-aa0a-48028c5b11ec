#!/usr/bin/env python3
"""
对敲测试数据生成器
生成符合对敲检测要求的测试参数
"""

import random
import json
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta


@dataclass
class TestCase:
    """单个测试用例"""
    case_id: int
    coin: str
    usdt_amount: float
    wait_seconds: int
    test_type: str  # 'normal', 'wash_trading_suspect'
    description: str


class WashTradingTestGenerator:
    """对敲测试数据生成器"""
    
    # 币种配置 - 根据您提供的信息
    COIN_RANGES = {
        'DOG': {'min': 1, 'max': 100, 'name': 'DOGE', 'emoji': '🐕'},
        'ETH': {'min': 50, 'max': 5000, 'name': 'ETH', 'emoji': '🔷'},
        'BTC': {'min': 5000, 'max': 100000, 'name': 'BTC', 'emoji': '₿'}
    }
    
    # 时间间隔范围
    TIME_RANGE = {'min': 1, 'max': 120}  # 1-120秒
    
    def __init__(self, seed: int = None):
        """初始化生成器
        
        Args:
            seed: 随机种子，用于可重现的测试
        """
        if seed is not None:
            random.seed(seed)
        
        self.generated_cases = []
    
    def generate_amount_for_coin(self, coin: str) -> float:
        """为指定币种生成随机交易金额"""
        if coin not in self.COIN_RANGES:
            raise ValueError(f"不支持的币种: {coin}")
        
        coin_range = self.COIN_RANGES[coin]
        
        # 使用对数分布，让小额交易更常见
        log_min = random.uniform(0, 1)
        amount = coin_range['min'] + (coin_range['max'] - coin_range['min']) * (log_min ** 2)
        
        # 根据币种特点调整精度
        if coin == 'DOG':
            return round(amount, 2)  # DOG保留2位小数
        elif coin == 'ETH':
            return round(amount, 1)  # ETH保留1位小数
        else:  # BTC
            return round(amount, 0)  # BTC取整数
    
    def generate_wait_time(self) -> int:
        """生成随机等待时间"""
        # 使用加权分布，让短时间间隔更常见（对敲特征）
        weights = [
            (1, 15, 0.4),    # 1-15秒，40%概率（高度可疑）
            (16, 30, 0.3),   # 16-30秒，30%概率（中度可疑）
            (31, 60, 0.2),   # 31-60秒，20%概率（低度可疑）
            (61, 120, 0.1)   # 61-120秒，10%概率（正常）
        ]
        
        rand = random.random()
        cumulative = 0
        
        for min_time, max_time, probability in weights:
            cumulative += probability
            if rand <= cumulative:
                return random.randint(min_time, max_time)
        
        return random.randint(1, 120)  # 兜底
    
    def generate_single_test_case(self, case_id: int, coin: str = None) -> TestCase:
        """生成单个测试用例"""
        if coin is None:
            coin = random.choice(list(self.COIN_RANGES.keys()))
        
        amount = self.generate_amount_for_coin(coin)
        wait_time = self.generate_wait_time()
        
        # 根据参数判断测试类型
        test_type = self.classify_test_case(coin, amount, wait_time)
        
        description = f"{self.COIN_RANGES[coin]['emoji']} {coin} {amount}U {wait_time}s"
        
        return TestCase(
            case_id=case_id,
            coin=coin,
            usdt_amount=amount,
            wait_seconds=wait_time,
            test_type=test_type,
            description=description
        )
    
    def classify_test_case(self, coin: str, amount: float, wait_time: int) -> str:
        """根据参数分类测试用例"""
        # 对敲检测的可疑特征：
        # 1. 时间间隔短（1-30秒）
        # 2. 金额在特定范围内
        
        if wait_time <= 30:
            if coin == 'DOG' and 10 <= amount <= 50:
                return 'wash_trading_suspect'
            elif coin == 'ETH' and 100 <= amount <= 1000:
                return 'wash_trading_suspect'
            elif coin == 'BTC' and 10000 <= amount <= 50000:
                return 'wash_trading_suspect'
        
        return 'normal'
    
    def generate_test_batch(self, total_cases: int = 5000, 
                           coin_distribution: Dict[str, float] = None) -> List[TestCase]:
        """生成一批测试用例
        
        Args:
            total_cases: 总测试用例数
            coin_distribution: 币种分布比例，如 {'DOG': 0.4, 'ETH': 0.4, 'BTC': 0.2}
        """
        if coin_distribution is None:
            # 默认分布：DOG 40%, ETH 40%, BTC 20%
            coin_distribution = {'DOG': 0.4, 'ETH': 0.4, 'BTC': 0.2}
        
        # 验证分布比例
        if abs(sum(coin_distribution.values()) - 1.0) > 0.01:
            raise ValueError("币种分布比例总和必须为1.0")
        
        test_cases = []
        
        for i in range(total_cases):
            # 根据分布选择币种
            rand = random.random()
            cumulative = 0
            selected_coin = None
            
            for coin, probability in coin_distribution.items():
                cumulative += probability
                if rand <= cumulative:
                    selected_coin = coin
                    break
            
            if selected_coin is None:
                selected_coin = list(coin_distribution.keys())[0]
            
            test_case = self.generate_single_test_case(i + 1, selected_coin)
            test_cases.append(test_case)
        
        self.generated_cases = test_cases
        return test_cases
    
    def get_statistics(self, test_cases: List[TestCase] = None) -> Dict[str, Any]:
        """获取测试用例统计信息"""
        if test_cases is None:
            test_cases = self.generated_cases
        
        if not test_cases:
            return {}
        
        stats = {
            'total_cases': len(test_cases),
            'coin_distribution': {},
            'amount_ranges': {},
            'time_distribution': {},
            'test_type_distribution': {},
            'wash_trading_suspects': 0
        }
        
        # 币种分布
        for coin in self.COIN_RANGES.keys():
            coin_cases = [case for case in test_cases if case.coin == coin]
            stats['coin_distribution'][coin] = {
                'count': len(coin_cases),
                'percentage': len(coin_cases) / len(test_cases) * 100
            }
        
        # 金额范围统计
        for coin in self.COIN_RANGES.keys():
            coin_cases = [case for case in test_cases if case.coin == coin]
            if coin_cases:
                amounts = [case.usdt_amount for case in coin_cases]
                stats['amount_ranges'][coin] = {
                    'min': min(amounts),
                    'max': max(amounts),
                    'avg': sum(amounts) / len(amounts)
                }
        
        # 时间分布
        time_ranges = [
            ('1-15s', 1, 15),
            ('16-30s', 16, 30),
            ('31-60s', 31, 60),
            ('61-120s', 61, 120)
        ]
        
        for range_name, min_time, max_time in time_ranges:
            count = len([case for case in test_cases 
                        if min_time <= case.wait_seconds <= max_time])
            stats['time_distribution'][range_name] = {
                'count': count,
                'percentage': count / len(test_cases) * 100
            }
        
        # 测试类型分布
        for test_type in ['normal', 'wash_trading_suspect']:
            count = len([case for case in test_cases if case.test_type == test_type])
            stats['test_type_distribution'][test_type] = {
                'count': count,
                'percentage': count / len(test_cases) * 100
            }
        
        stats['wash_trading_suspects'] = stats['test_type_distribution']['wash_trading_suspect']['count']
        
        return stats
    
    def save_test_cases(self, test_cases: List[TestCase], filename: str):
        """保存测试用例到文件"""
        data = {
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'total_cases': len(test_cases),
                'generator_version': '1.0'
            },
            'statistics': self.get_statistics(test_cases),
            'test_cases': [
                {
                    'case_id': case.case_id,
                    'coin': case.coin,
                    'usdt_amount': case.usdt_amount,
                    'wait_seconds': case.wait_seconds,
                    'test_type': case.test_type,
                    'description': case.description
                }
                for case in test_cases
            ]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def load_test_cases(self, filename: str) -> List[TestCase]:
        """从文件加载测试用例"""
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        test_cases = []
        for case_data in data['test_cases']:
            test_case = TestCase(
                case_id=case_data['case_id'],
                coin=case_data['coin'],
                usdt_amount=case_data['usdt_amount'],
                wait_seconds=case_data['wait_seconds'],
                test_type=case_data['test_type'],
                description=case_data['description']
            )
            test_cases.append(test_case)
        
        return test_cases
    
    def print_statistics(self, test_cases: List[TestCase] = None):
        """打印统计信息"""
        stats = self.get_statistics(test_cases)
        
        if not stats:
            print("❌ 没有测试用例数据")
            return
        
        print("📊 对敲测试用例统计")
        print("=" * 50)
        print(f"总测试用例数: {stats['total_cases']}")
        print()
        
        print("🪙 币种分布:")
        for coin, data in stats['coin_distribution'].items():
            emoji = self.COIN_RANGES[coin]['emoji']
            print(f"  {emoji} {coin}: {data['count']} ({data['percentage']:.1f}%)")
        print()
        
        print("💰 金额范围:")
        for coin, data in stats['amount_ranges'].items():
            emoji = self.COIN_RANGES[coin]['emoji']
            print(f"  {emoji} {coin}: {data['min']:.1f} - {data['max']:.1f} USDT (平均: {data['avg']:.1f})")
        print()
        
        print("⏰ 时间分布:")
        for range_name, data in stats['time_distribution'].items():
            print(f"  {range_name}: {data['count']} ({data['percentage']:.1f}%)")
        print()
        
        print("🔍 测试类型:")
        for test_type, data in stats['test_type_distribution'].items():
            type_name = "正常交易" if test_type == 'normal' else "疑似对敲"
            print(f"  {type_name}: {data['count']} ({data['percentage']:.1f}%)")
        print()
        
        print(f"⚠️  疑似对敲用例: {stats['wash_trading_suspects']} 个")


def main():
    """主函数 - 测试数据生成器"""
    print("🎲 对敲测试数据生成器")
    print("=" * 40)
    
    generator = WashTradingTestGenerator(seed=42)  # 使用固定种子确保可重现
    
    while True:
        print("\n请选择:")
        print("1. 生成测试用例 (5000个)")
        print("2. 生成测试用例 (自定义数量)")
        print("3. 生成单个测试用例")
        print("4. 查看统计信息")
        print("5. 保存测试用例")
        print("6. 加载测试用例")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-6): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            print("🎲 生成5000个测试用例...")
            test_cases = generator.generate_test_batch(5000)
            print(f"✅ 生成完成！")
            generator.print_statistics(test_cases)
        elif choice == '2':
            try:
                count = int(input("请输入测试用例数量: "))
                if count > 0:
                    print(f"🎲 生成{count}个测试用例...")
                    test_cases = generator.generate_test_batch(count)
                    print(f"✅ 生成完成！")
                    generator.print_statistics(test_cases)
                else:
                    print("❌ 数量必须大于0")
            except ValueError:
                print("❌ 请输入有效数字")
        elif choice == '3':
            coin = input("请输入币种 (DOG/ETH/BTC，留空随机): ").upper() or None
            if coin and coin not in generator.COIN_RANGES:
                print("❌ 不支持的币种")
                continue
            
            test_case = generator.generate_single_test_case(1, coin)
            print(f"生成的测试用例: {test_case.description}")
            print(f"  币种: {test_case.coin}")
            print(f"  金额: {test_case.usdt_amount} USDT")
            print(f"  等待时间: {test_case.wait_seconds} 秒")
            print(f"  类型: {test_case.test_type}")
        elif choice == '4':
            if generator.generated_cases:
                generator.print_statistics()
            else:
                print("❌ 请先生成测试用例")
        elif choice == '5':
            if generator.generated_cases:
                filename = input("请输入保存文件名 (默认: test_cases.json): ").strip()
                if not filename:
                    filename = "test_cases.json"
                generator.save_test_cases(generator.generated_cases, filename)
                print(f"✅ 测试用例已保存到 {filename}")
            else:
                print("❌ 请先生成测试用例")
        elif choice == '6':
            filename = input("请输入文件名: ").strip()
            try:
                test_cases = generator.load_test_cases(filename)
                generator.generated_cases = test_cases
                print(f"✅ 已加载 {len(test_cases)} 个测试用例")
                generator.print_statistics(test_cases)
            except FileNotFoundError:
                print("❌ 文件不存在")
            except Exception as e:
                print(f"❌ 加载失败: {e}")
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    main()
