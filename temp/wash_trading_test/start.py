#!/usr/bin/env python3
"""
对敲检测测试系统启动脚本
提供快速启动和预检查功能
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'trading_bot'))

from config import get_config, validate_config, print_config_summary


def check_environment():
    """检查运行环境"""
    print("🔍 环境检查")
    print("-" * 30)
    
    checks = []
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version >= (3, 7):
        checks.append(("✅", f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}"))
    else:
        checks.append(("❌", f"Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro} (需要3.7+)"))
    
    # 检查必要模块
    required_modules = [
        'sqlite3', 'json', 'csv', 'datetime', 'threading', 
        'queue', 'statistics', 'dataclasses', 'typing'
    ]
    
    for module in required_modules:
        try:
            __import__(module)
            checks.append(("✅", f"模块 {module}: 可用"))
        except ImportError:
            checks.append(("❌", f"模块 {module}: 缺失"))
    
    # 检查trading_bot配置
    try:
        from utils.config import load_config
        config = load_config('config.yaml')
        if config.get('api', {}).get('token'):
            checks.append(("✅", "Trading Bot配置: 已配置"))
        else:
            checks.append(("⚠️", "Trading Bot配置: 缺少token"))
    except Exception as e:
        checks.append(("❌", f"Trading Bot配置: 错误 - {e}"))
    
    # 检查写入权限
    try:
        test_file = "test_write_permission.tmp"
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        checks.append(("✅", "文件写入权限: 正常"))
    except Exception as e:
        checks.append(("❌", f"文件写入权限: 错误 - {e}"))
    
    # 显示检查结果
    for status, message in checks:
        print(f"  {status} {message}")
    
    # 统计结果
    success_count = sum(1 for status, _ in checks if status == "✅")
    warning_count = sum(1 for status, _ in checks if status == "⚠️")
    error_count = sum(1 for status, _ in checks if status == "❌")
    
    print(f"\n📊 检查结果: {success_count} 成功, {warning_count} 警告, {error_count} 错误")
    
    return error_count == 0


def show_system_info():
    """显示系统信息"""
    print("ℹ️  系统信息")
    print("-" * 30)
    
    print(f"🎯 对敲检测测试系统 v1.0")
    print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 工作目录: {os.getcwd()}")
    print(f"🐍 Python路径: {sys.executable}")
    print(f"💾 系统平台: {sys.platform}")
    
    # 显示支持的币种
    coin_config = get_config('coin')
    print(f"\n💰 支持币种:")
    for coin, config in coin_config.items():
        print(f"  {config['emoji']} {coin}: {config['min_usdt']}-{config['max_usdt']} USDT")
    
    # 显示测试范围
    test_config = get_config('test')
    print(f"\n🎲 测试配置:")
    print(f"  默认测试数量: {test_config['default_test_count']}")
    print(f"  币种分布: DOG {test_config['default_coin_distribution']['DOG']*100}%, "
          f"ETH {test_config['default_coin_distribution']['ETH']*100}%, "
          f"BTC {test_config['default_coin_distribution']['BTC']*100}%")


def show_quick_start_guide():
    """显示快速开始指南"""
    print("🚀 快速开始指南")
    print("-" * 30)
    
    print("1️⃣  环境准备:")
    print("   • 确保trading_bot/config.yaml已正确配置")
    print("   • 确保API token有效且有足够权限")
    print("   • 确保账户有足够资金进行测试")
    
    print("\n2️⃣  运行测试:")
    print("   • 快速测试: 选择菜单项 '2' (5个测试)")
    print("   • 完整测试: 选择菜单项 '3' (5000个测试)")
    print("   • 自定义测试: 选择菜单项 '4' (自定义数量)")
    
    print("\n3️⃣  查看结果:")
    print("   • 测试记录: 选择菜单项 '6'")
    print("   • 分析结果: 选择菜单项 '5'")
    print("   • 导出数据: 在查看结果时选择导出")
    
    print("\n⚠️  重要提示:")
    print("   • 所有测试都是真实交易，会产生实际费用")
    print("   • 建议先运行快速测试验证系统正常")
    print("   • 大规模测试前请确保充足的资金和时间")


def show_safety_warnings():
    """显示安全警告"""
    print("⚠️  安全警告")
    print("-" * 30)
    
    warnings = [
        "本系统执行真实交易，会产生实际的交易费用",
        "大量交易可能对市场价格产生影响",
        "请确保账户资金充足，避免交易失败",
        "建议在低峰时段进行大规模测试",
        "测试过程中请保持网络连接稳定",
        "如遇异常情况，请及时停止测试",
        "重要数据请及时备份和导出"
    ]
    
    for i, warning in enumerate(warnings, 1):
        print(f"   {i}. {warning}")
    
    print(f"\n🛡️  安全配置:")
    security_config = get_config('security')
    print(f"   • 单次测试最大金额: {security_config['max_single_test_amount']} USDT")
    print(f"   • 每日测试限制: {security_config['daily_test_limit']} 次")
    print(f"   • 需要确认的金额阈值: {security_config['require_confirmation_above']} USDT")


def main():
    """主函数"""
    print("🎯 对敲检测测试系统启动器")
    print("=" * 60)
    
    # 显示系统信息
    show_system_info()
    print()
    
    # 环境检查
    env_ok = check_environment()
    print()
    
    # 配置验证
    print("⚙️  配置验证")
    print("-" * 30)
    config_errors = validate_config()
    if config_errors:
        print("❌ 配置验证失败:")
        for error in config_errors:
            print(f"   • {error}")
        env_ok = False
    else:
        print("✅ 配置验证通过")
    print()
    
    # 显示安全警告
    show_safety_warnings()
    print()
    
    # 显示快速开始指南
    show_quick_start_guide()
    print()
    
    # 启动选择
    if not env_ok:
        print("❌ 环境检查失败，请解决上述问题后重试")
        return
    
    print("🚀 启动选项")
    print("-" * 30)
    print("1. 启动主程序")
    print("2. 查看详细配置")
    print("3. 仅生成测试用例预览")
    print("4. 检查交易接口")
    print("0. 退出")
    
    while True:
        choice = input("\n请选择 (0-4): ").strip()
        
        if choice == '0':
            print("👋 退出启动器")
            break
        elif choice == '1':
            print("\n🚀 启动主程序...")
            print("=" * 60)
            try:
                from main import main as main_program
                main_program()
            except KeyboardInterrupt:
                print("\n👋 程序被用户中断")
            except Exception as e:
                print(f"\n❌ 程序启动失败: {e}")
            break
        elif choice == '2':
            print()
            print_config_summary()
        elif choice == '3':
            print("\n🎲 生成测试用例预览...")
            try:
                from test_data_generator import WashTradingTestGenerator
                generator = WashTradingTestGenerator()
                test_cases = generator.generate_test_batch(20)
                generator.print_statistics(test_cases)
                
                print(f"\n📋 测试用例预览 (前10个):")
                for i, case in enumerate(test_cases[:10], 1):
                    print(f"  {i}. {case.description} ({case.test_type})")
            except Exception as e:
                print(f"❌ 生成预览失败: {e}")
        elif choice == '4':
            print("\n🔗 检查交易接口...")
            try:
                from unified_trader import UnifiedTrader
                trader = UnifiedTrader()
                trader.show_coin_ranges()
                print("✅ 交易接口检查完成")
            except Exception as e:
                print(f"❌ 交易接口检查失败: {e}")
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 启动器被用户中断")
    except Exception as e:
        print(f"\n❌ 启动器错误: {e}")
